<!--
  筛选结果列表组件
-->
<template>
  <div class="results-list-panel glass-panel">
    <div class="section-header">
      <h2>
        <i class="fas fa-list"></i>
        筛选结果
        <span v-if="totalCount > 0" class="count-badge">{{ totalCount }}</span>
      </h2>
      <div class="result-actions" v-if="filteredTexts.length > 0">
        <ModernButton
          text="全选"
          icon="fas fa-check-square"
          @click="$emit('selectAll')"
          :disabled="selectedTextsCount === filteredTexts.length"
        />
        <ModernButton
          text="清空选择"
          icon="fas fa-times"
          @click="$emit('clearSelection')"
          :disabled="selectedTextsCount === 0"
        />
      </div>
    </div>

    <!-- 结果列表 -->
    <div class="results-list" v-if="filteredTexts.length > 0 && !isLoading">
      <ResultItem
        v-for="(text, index) in filteredTexts"
        :key="text.id"
        :item="text"
        :index="index + 1"
        :is-selected="isSelected(text.id)"
        @toggle-selection="$emit('toggleSelection', text.id)"
      />
    </div>

    <!-- 骨架屏加载状态 -->
    <div class="results-list" v-else-if="isLoading && (filteredTexts.length > 0 || hasFilterConditions)">
      <div class="skeleton-item" v-for="n in skeletonCount" :key="`skeleton-${n}`">
        <!-- 序号角标 -->
        <div class="skeleton-index"></div>

        <!-- 头部区域 -->
        <div class="skeleton-header">
          <div class="skeleton-checkbox"></div>
          <div class="skeleton-id"></div>
          <div class="skeleton-labels">
            <div class="skeleton-label" v-for="i in Math.floor(Math.random() * 3) + 1" :key="`label-${i}`"></div>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="skeleton-content">
          <div class="skeleton-text skeleton-text-long"></div>
          <div class="skeleton-text skeleton-text-medium"></div>
          <div class="skeleton-text skeleton-text-short" v-if="Math.random() > 0.5"></div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!isLoading" class="empty-state">
      <i class="fas fa-search"></i>
      <p>{{ hasFilterConditions ? '没有找到匹配的文本' : '请设置筛选条件后执行筛选' }}</p>
    </div>

    <!-- 首次加载状态 -->
    <div v-else-if="isLoading && !hasFilterConditions" class="loading-state">
      <i class="fas fa-spinner fa-spin"></i>
      <p>正在筛选数据...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { AnnotationDataResponse } from '@/types/api'
import ModernButton from '../common/ModernButton.vue'
import ResultItem from './ResultItem.vue'

// Props
interface Props {
  filteredTexts: AnnotationDataResponse[]
  totalCount: number
  selectedTextsCount: number
  isLoading: boolean
  hasFilterConditions: boolean
  isSelected: (id: number) => boolean
}

const props = defineProps<Props>()

// Emits
interface Emits {
  'selectAll': []
  'clearSelection': []
  'toggleSelection': [id: number]
}

defineEmits<Emits>()

// 计算骨架屏显示数量
const skeletonCount = computed(() => {
  // 如果有现有数据，使用现有数据的数量，最多显示10个骨架屏
  if (props.filteredTexts.length > 0) {
    return Math.min(props.filteredTexts.length, 10)
  }
  // 否则显示默认数量
  return 5
})
</script>

<style scoped>
.results-list-panel {
  padding: var(--spacing-xl);
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  flex-shrink: 0;
}

.section-header h2 {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.count-badge {
  background: var(--el-color-primary);
  color: white;
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  margin-left: var(--spacing-sm);
}

.result-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.results-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  margin-top: var(--spacing-md);
  padding-right: 8px;
  min-height: 0;
}



/* 空状态和加载状态 */
.empty-state, .loading-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4xl);
  color: var(--el-text-color-placeholder);
  text-align: center;
}

.empty-state i, .loading-state i {
  font-size: 48px;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.loading-state i {
  color: var(--el-color-primary);
  opacity: 0.8;
}

/* 骨架屏样式 */
.skeleton-item {
  position: relative;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: var(--radius-md);
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

/* 序号角标 */
.skeleton-index {
  position: absolute;
  top: 0px;
  right: 0px;
  width: 36px;
  height: 24px;
  background: var(--el-border-color-light);
  border-radius: 0 var(--radius-md);
}

/* 头部区域 */
.skeleton-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.skeleton-checkbox {
  width: 16px;
  height: 16px;
  background: var(--el-border-color-light);
  border-radius: 2px;
  flex-shrink: 0;
}

.skeleton-id {
  width: 50px;
  height: 14px;
  background: var(--el-border-color-light);
  border-radius: 2px;
}

.skeleton-labels {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
  flex: 1;
}

.skeleton-label {
  width: 60px;
  height: 20px;
  background: var(--el-border-color-light);
  border-radius: var(--radius-sm);
}

/* 内容区域 */
.skeleton-content {
  padding-left: 26px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.skeleton-text {
  height: 16px;
  background: var(--el-border-color-light);
  border-radius: 2px;
}

.skeleton-text-long {
  width: 100%;
}

.skeleton-text-medium {
  width: 85%;
}

.skeleton-text-short {
  width: 60%;
}

@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 自定义滚动条样式 */
.results-list::-webkit-scrollbar {
  width: 6px;
}

.results-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background var(--duration-fast) ease;
}

.results-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
</style> 